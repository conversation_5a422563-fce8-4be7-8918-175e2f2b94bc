package com.example.tesseractocr.config;

import net.sourceforge.tess4j.Tesseract;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TesseractOcrConfiguration {

	@Value("${tess4j.datapath}")
	private String dataPath;

	@Bean
	public Tesseract tesseract() {
		// 设置 Tesseract 可执行文件路径
		System.setProperty("jna.library.path", "/opt/homebrew/lib:/opt/local/lib");
		System.setProperty("java.library.path", "/opt/homebrew/lib:/opt/local/lib");

		Tesseract tesseract = new Tesseract();
		// 设置训练数据文件夹路径
		tesseract.setDatapath(dataPath);
		// 设置为中文简体
		tesseract.setLanguage("chi_sim");
		return tesseract;
	}
}
