#!/bin/bash

# 设置环境变量
export DYLD_LIBRARY_PATH="/opt/local/lib:$DYLD_LIBRARY_PATH"
export LD_LIBRARY_PATH="/opt/local/lib:$LD_LIBRARY_PATH"
export TESSDATA_PREFIX="/Users/<USER>/Downloads/tessdata"

# 设置 JVM 参数
JAVA_OPTS="-Djna.library.path=/opt/local/lib"
JAVA_OPTS="$JAVA_OPTS -Djava.library.path=/opt/local/lib"
JAVA_OPTS="$JAVA_OPTS -Dtessdata.path=/Users/<USER>/Downloads/tessdata"
JAVA_OPTS="$JAVA_OPTS -Djna.tmpdir=/tmp"
JAVA_OPTS="$JAVA_OPTS -Djava.io.tmpdir=/tmp"
JAVA_OPTS="$JAVA_OPTS -Djna.nosys=false"
JAVA_OPTS="$JAVA_OPTS -Djna.noclasspath=false"
JAVA_OPTS="$JAVA_OPTS -Djna.debug_load=true"

echo "Starting Tesseract OCR application..."
echo "DYLD_LIBRARY_PATH: $DYLD_LIBRARY_PATH"
echo "TESSDATA_PREFIX: $TESSDATA_PREFIX"
echo "JAVA_OPTS: $JAVA_OPTS"

# 运行应用
java $JAVA_OPTS -jar target/TesseractOCR-0.0.1-SNAPSHOT.jar
