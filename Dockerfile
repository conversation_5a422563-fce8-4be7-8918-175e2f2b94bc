# 使用支持 x86_64 的 OpenJDK 基础镜像
FROM --platform=linux/amd64 openjdk:21-jdk-slim

# 安装 Tesseract 和必要的依赖
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    libtesseract-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 JAR 文件
COPY target/TesseractOCR-0.0.1-SNAPSHOT.jar app.jar

# 设置环境变量
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/

# 暴露端口
EXPOSE 8889

# 启动应用
CMD ["java", "-jar", "app.jar"]
